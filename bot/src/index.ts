import { Bot, Context } from "grammy";
import { checkGenerationAllowed, decrementBothLimits, getUserLimits, getGlobalSettings, logGeneration } from "../utils/supabase";
import { generateImage } from "../utils/openai";

const bot = new Bot(process.env.TELEGRAM_BOT_TOKEN!);

bot.command("start", (ctx) => {
  ctx.reply(
    "🎨 Welcome to the AI Image Generator Bot!\n\n" +
    "Commands:\n" +
    "• /generate [prompt] - Generate an image\n" +
    "• /limit - Check remaining generations\n" +
    "• /help - Show this help message\n\n" +
    "Try: /generate a cat in space"
  );
});

bot.command("help", (ctx) => {
  ctx.reply(
    "🤖 AI Image Generator Bot Help\n\n" +
    "Commands:\n" +
    "• /generate [prompt] - Generate an image from your text description\n" +
    "• /limit - Check how many generations you have left today\n" +
    "• /help - Show this help message\n\n" +
    "Examples:\n" +
    "• /generate a sunset over mountains\n" +
    "• /generate a futuristic city\n" +
    "• /generate a cute robot\n\n" +
    "Features:\n" +
    "• AI-powered image generation using DALL-E 3\n" +
    "• Automatic logo watermarking\n" +
    "• Rate limiting: Both per-user and global daily limits apply"
  );
});

bot.command("limit", async (ctx) => {
  const userId = ctx.from?.id;
  if (!userId) {
    await ctx.reply("❌ Unable to identify user");
    return;
  }

  const userLimits = await getUserLimits(userId);
  const globalSettings = await getGlobalSettings();
  
  if (!userLimits || !globalSettings) {
    await ctx.reply("❌ Error checking your limits. Please try again.");
    return;
  }

  await ctx.reply(
    `📊 Generation Limits Status\n\n` +
    `👤 Your Personal Limit:\n` +
    `• Daily limit: ${userLimits.daily_limit} images\n` +
    `• Remaining: ${userLimits.remaining} images\n\n` +
    `🌐 Global System Limit:\n` +
    `• Daily capacity: ${globalSettings.daily_limit} images\n` +
    `• Remaining: ${globalSettings.remaining} images\n\n` +
    `• Resets: Daily at midnight UTC\n` +
    `• Both limits must have capacity for generation`
  );
});

bot.command("generate", async (ctx: Context) => {
  const userId = ctx.from?.id;
  if (!userId) {
    await ctx.reply("❌ Unable to identify user");
    return;
  }

  const prompt = ctx.message?.text?.split(" ").slice(1).join(" ");
  if (!prompt) {
    await ctx.reply("❌ Please provide a prompt. Example: /generate a cat in space");
    return;
  }

  // Check both user and global limits
  const generationCheck = await checkGenerationAllowed(userId);
  if (!generationCheck) {
    await ctx.reply("❌ Error checking your limits. Please try again.");
    return;
  }

  if (!generationCheck.allowed) {
    let message = "";
    
    if (generationCheck.reason === 'user_limit_exceeded') {
      message = "🚫 Your daily limit exceeded!\n\n" +
        `You've used all ${generationCheck.user_limit} of your daily image generations.\n` +
        `Global system has ${generationCheck.global_remaining} images remaining.\n\n` +
        "Your limit resets daily at midnight UTC.";
    } else if (generationCheck.reason === 'global_limit_exceeded') {
      message = "🚫 System daily limit exceeded!\n\n" +
        `The global daily capacity of ${generationCheck.global_limit} images has been reached.\n` +
        `You have ${generationCheck.user_remaining} personal generations remaining.\n\n` +
        "The system resets daily at midnight UTC.";
    }
    
    await ctx.reply(message);
    return;
  }

  // Send "generating" message
  const generatingMessage = await ctx.reply("🎨 Generating your image, please wait...");

  try {
    // Enhanced prompt with specific art style
    const enhancedPrompt = `Retro cartoon illustration. Thick black outlines, smooth flat shading, limited warm vintage palette (muted oranges, ochres, teal accents). 1950s newspaper comic style, rounded shapes, subtle paper-grain texture. Clean vector aesthetic, high-resolution, simple background with soft abstract swirls in tan. ${prompt}`;
    
    // Generate image
    const result = await generateImage(enhancedPrompt);

    if (result.success && result.imageUrl) {
      // Decrement both user and global limits atomically
      const decrementSuccess = await decrementBothLimits(userId);
      
      if (!decrementSuccess) {
        await ctx.reply("❌ Limits changed while generating. Please try again.");
        return;
      }
      
      // Log successful generation
      await logGeneration(userId, prompt, true, result.imageUrl);

      // Send the image
      await ctx.replyWithPhoto(result.imageUrl, {
        caption: `🎨 Generated: "${prompt}"\n\n` +
          `Your remaining: ${generationCheck.user_remaining - 1} | ` +
          `Global remaining: ${generationCheck.global_remaining - 1}\n\n` +
          `✨ Watermarked with CryBaby logo`
      });

      // Delete the "generating" message
      if (ctx.chat) {
        await ctx.api.deleteMessage(ctx.chat.id, generatingMessage.message_id);
      }
    } else {
      // Log failed generation (don't decrement on failure)
      await logGeneration(userId, prompt, false, undefined, result.error);

      await ctx.reply(
        `❌ Failed to generate image: ${result.error}\n\n` +
        "Please try again with a different prompt."
      );
    }
  } catch (error) {
    console.error("Error in generate command:", error);
    
    // Log failed generation
    await logGeneration(userId, prompt, false, undefined, "Unexpected error");

    await ctx.reply(
      "❌ An unexpected error occurred while generating your image.\n" +
      "Please try again later."
    );
  }
});

// Error handler
bot.catch((err) => {
  console.error("Bot error:", err);
});

bot.start();
console.log("🤖 Bot started successfully!");