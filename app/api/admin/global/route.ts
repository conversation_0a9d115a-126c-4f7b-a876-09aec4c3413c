import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

export async function GET() {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { data, error } = await supabase
      .rpc('check_and_reset_global_daily_limit');

    if (error) {
      console.error('Error with global settings:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Internal server error:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { daily_limit } = await request.json();

    if (!daily_limit || daily_limit < 1) {
      return NextResponse.json(
        { error: "Daily limit must be at least 1" },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from("cry_global_settings")
      .update({
        daily_limit,
        remaining: daily_limit,
        last_reset: new Date().toISOString().split("T")[0]
      })
      .eq("id", 1)
      .select()
      .single();

    if (error) {
      console.error('Error with global settings:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Internal server error:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}