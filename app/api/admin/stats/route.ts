import { NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";

export async function GET() {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const today = new Date().toISOString().split("T")[0];
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0];

    // Get global settings
    const { data: globalSettings } = await supabase
      .rpc('check_and_reset_global_daily_limit');

    // Get total users
    const { count: totalUsers } = await supabase
      .from("cry_user_limits")
      .select("*", { count: "exact", head: true });

    // Get active users today
    const { count: activeToday } = await supabase
      .from("cry_generation_logs")
      .select("*", { count: "exact", head: true })
      .gte("timestamp", today);

    // Get total generations
    const { count: totalGenerations } = await supabase
      .from("cry_generation_logs")
      .select("*", { count: "exact", head: true });

    // Get successful generations
    const { count: successfulGenerations } = await supabase
      .from("cry_generation_logs")
      .select("*", { count: "exact", head: true })
      .eq("success", true);

    // Get today's generations
    const { count: todayGenerations } = await supabase
      .from("cry_generation_logs")
      .select("*", { count: "exact", head: true })
      .gte("timestamp", today);

    // Get daily stats for the last 7 days
    const { data: dailyStats } = await supabase
      .from("cry_generation_logs")
      .select("timestamp, success")
      .gte("timestamp", sevenDaysAgo)
      .order("timestamp", { ascending: true });

    // Process daily stats
    const dailyStatsMap = new Map();
    for (let i = 6; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split("T")[0];
      dailyStatsMap.set(date, { date, total: 0, successful: 0 });
    }

    dailyStats?.forEach(log => {
      const date = log.timestamp.split("T")[0];
      if (dailyStatsMap.has(date)) {
        const stats = dailyStatsMap.get(date);
        stats.total++;
        if (log.success) stats.successful++;
      }
    });

    return NextResponse.json({
      // User statistics
      totalUsers: totalUsers || 0,
      activeToday: activeToday || 0,
      
      // Global limit statistics
      globalSettings: globalSettings || {
        daily_limit: 0,
        remaining: 0,
        last_reset: today
      },
      globalUsedToday: globalSettings ? globalSettings.daily_limit - globalSettings.remaining : 0,
      globalUtilization: globalSettings ? 
        ((globalSettings.daily_limit - globalSettings.remaining) / globalSettings.daily_limit * 100).toFixed(1) : "0",
      
      // Generation statistics
      totalGenerations: totalGenerations || 0,
      successfulGenerations: successfulGenerations || 0,
      todayGenerations: todayGenerations || 0,
      successRate: totalGenerations ? ((successfulGenerations || 0) / totalGenerations * 100).toFixed(1) : "0",
      
      // Daily breakdown
      dailyStats: Array.from(dailyStatsMap.values())
    });
  } catch (error) {
    console.error('Internal server error:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}